# 发票信息管理功能

## 概述

发票信息管理功能已从合同模块迁移到客户模块，并按照苹果设计规范进行了重构。该功能支持发票信息的创建、编辑、查看和导出。

## 功能特性

### 1. 数据筛选
- **分账序号筛选**: 下拉选择，显示格式为"分账序号名称(分账序号)"
- **创建者筛选**: 文本输入
- **发票名称筛选**: 文本输入
- **重置功能**: 一键清空所有筛选条件

### 2. 发票信息管理
- **新建发票**: 通过Drawer抽屉组件，参考订单新建样式
- **编辑发票**: 支持修改现有发票信息
- **查看详情**: 通过Dialog弹框，参考客户详情样式
- **数据导出**: 支持CSV格式导出

### 3. 表单验证
- **必填字段验证**: 分账序号、开票名称、税号、开账日期
- **条件验证**: 邮寄方式为快递/邮件时，邮寄地址必填
- **日期验证**: 开账开始日期必须早于结束日期
- **422错误处理**: 后端验证错误的友好提示

### 4. 界面设计
- **苹果设计规范**: 遵循Apple Design System
- **响应式布局**: 支持不同屏幕尺寸
- **加载状态**: 提交时显示加载动画
- **错误提示**: 友好的错误信息展示

## API接口

### 1. 发票列表查询
```
GET /invoice-info
参数:
- account_seq: 分账序号
- create_user: 创建者
- customer_invoice_name: 发票名称
- page: 页码
- pageSize: 每页数量
```

### 2. 分账序号简单列表
```
GET /account-seq/simple-list
返回格式:
{
  "data": [
    {
      "id": 1,
      "account_seq": "FZ-**********",
      "seq_name": "分账序号名称"
    }
  ]
}
```

### 3. 发票信息操作
```
POST /invoice-info     # 新建
PUT /invoice-info/{id} # 编辑
GET /invoice-info/{id} # 详情
```

## 数据结构

### 发票信息字段
- `customer_invoice_name`: 开票名称 (必填)
- `customer_tax_number`: 税号 (必填)
- `account_seq`: 分账序号 (必填)
- `customer_invoice_type`: 发票类型
- `customer_verify_type`: 对账类型
- `customer_fare_order`: 票款顺序
- `is_unusual_need`: 是否有特殊需求
- `unusual_need_explain`: 特殊需求说明
- `is_open_charge`: 开账状态
- `postal_type`: 邮寄方式
- `postal_address`: 邮寄地址 (条件必填)
- `charge_start_day`: 开账开始日期 (必填)
- `charge_end_day`: 开账结束日期 (必填)

### 银行信息字段
- `customer_deposit_bank`: 开户银行
- `customer_deposit_bank_sub`: 开户支行
- `customer_bank_account_name`: 银行账户名
- `customer_bank_account`: 银行账号

## 使用说明

### 1. 筛选发票
1. 选择分账序号（可选）
2. 输入创建者（可选）
3. 输入发票名称（可选）
4. 点击"搜索"按钮
5. 点击"重置"按钮清空筛选条件

### 2. 新建发票
1. 点击"新建"按钮
2. 填写必填字段（分账序号、开票名称、税号、开账日期）
3. 根据需要填写其他信息
4. 如选择快递/邮件，必须填写邮寄地址
5. 点击"保存"按钮

### 3. 编辑发票
1. 点击表格中的编辑按钮
2. 修改需要更新的字段
3. 点击"保存"按钮

### 4. 查看详情
1. 点击表格中的查看按钮
2. 在弹框中查看完整的发票信息
3. 点击"关闭"按钮退出

### 5. 导出数据
1. 点击"导出"按钮
2. 系统将当前筛选结果导出为CSV文件
3. 文件包含开票名称、分账序号、税号等主要信息

## 技术实现

### 1. 组件结构
- `InvoiceInfo.vue`: 主组件
- `InvoiceInfo.test.ts`: 单元测试

### 2. 类型定义
- `InvoiceInfoItem`: 发票信息项
- `InvoiceFormData`: 表单数据
- `InvoiceSearchParams`: 搜索参数
- `AccountSeqSimpleInfo`: 分账序号简单信息

### 3. API服务
- `getInvoiceList`: 获取发票列表
- `getAccountSeqSimpleList`: 获取分账序号列表
- `createInvoice`: 创建发票
- `updateInvoice`: 更新发票
- `getInvoiceDetail`: 获取发票详情

### 4. 样式设计
- 遵循Apple Design System
- 使用PrimeVue组件库
- 响应式布局设计
- 统一的颜色和字体规范

## 注意事项

1. **权限控制**: 新建和编辑功能需要操作权限
2. **数据验证**: 前端和后端都有相应的验证机制
3. **错误处理**: 422错误会显示具体的验证信息
4. **性能优化**: 使用懒加载和分页机制
5. **用户体验**: 提供加载状态和友好的错误提示

## 更新日志

### v1.0.0 (2025-01-12)
- 从合同模块迁移到客户模块
- 重构UI界面，遵循苹果设计规范
- 更新API接口，支持新的筛选参数
- 添加表单验证和错误处理
- 实现导出功能
- 添加单元测试
