/**
 * 格式化日期为YYYYMM格式的数字
 * @param date 要格式化的日期对象
 * @returns 格式化后的数字，如202401表示2024年1月
 */
export const formatDateToYYYYMM = (date: Date | null): number => {
  if (!date) return 0;
  const year = date.getFullYear();
  const month = date.getMonth() + 1;
  return parseInt(`${year}${month.toString().padStart(2, '0')}`);
};

// 示例用法：
// const date = new Date(2024, 0, 15); // 2024年1月15日
// const formatted = formatDateToYYYYMM(date); // 返回 202401
