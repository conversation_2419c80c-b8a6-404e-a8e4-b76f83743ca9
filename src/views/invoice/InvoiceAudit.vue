<script setup lang="ts">
import { ref, onMounted } from "vue";
import { useToast } from "primevue/usetoast";
import { formatDateTime } from "../../utils/common";
import { getCustomersSimpleList } from "../../services/customer";
import {
  getPreInvoicingDetail,
  getInvoiceAuditList,
  exportInvoiceAudit,
  revertAuditToPending,
} from "../../services/invoice";
import type { CustomerSimpleInfo } from "../../types/customer";
import type {
  PreInvoicingDetailItem,
  PreInvoicingDetailSearchParams,
  InvoiceAuditItem,
  InvoiceAuditSearchParams,
} from "../../types/invoice";

const toast = useToast();
const loading = ref(false);
const exportLoading = ref(false);
const items = ref<InvoiceAuditItem[]>([]);
const totalRecords = ref(0);

// 筛选表单
const searchForm = ref<InvoiceAuditSearchParams>({
  account_seq: "",
  created_at: "",
  customer_num: "",
  group_approved_state: "",
  page: 1,
  pageSize: 20,
});

// 开票时间日期对象
const createdAtDate = ref<Date | null>(null);

// 客户选项
const customerOptions = ref<CustomerSimpleInfo[]>([]);

// 审批状态选项
const approvalStatusOptions = [
  { label: "审批中", value: "submitted" },
  { label: "已审批", value: "approved" },
  { label: "被驳回", value: "rejected" },
];

// 行展开相关状态
const expandedRows = ref<InvoiceAuditItem[]>([]);
const detailData = ref<Record<number, PreInvoicingDetailItem[]>>({});
const detailLoading = ref<Record<number, boolean>>({});
const detailTotalRecords = ref<Record<number, number>>({});
const detailSearchParams = ref<Record<number, PreInvoicingDetailSearchParams>>(
  {}
);

// 处理开票时间日期变化
const onCreatedAtChange = (date: Date | null) => {
  createdAtDate.value = date;
  if (date) {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, "0");
    const day = String(date.getDate()).padStart(2, "0");
    searchForm.value.created_at = `${year}-${month}-${day}`;
  } else {
    searchForm.value.created_at = "";
  }
};

// 加载客户列表
const loadCustomerOptions = async () => {
  try {
    const response = await getCustomersSimpleList();
    if (response.code === 200) {
      customerOptions.value = response.data;
    }
  } catch (error) {
    toast.add({
      severity: "error",
      summary: "错误",
      detail: "加载客户列表失败",
      life: 3000,
    });
  }
};

// 获取审核数据
const loadData = async () => {
  loading.value = true;
  try {
    const params = {
      ...searchForm.value,
      page: searchForm.value.page || 1,
      pageSize: searchForm.value.pageSize || 20,
    };

    // 移除空值参数
    Object.keys(params).forEach((key) => {
      const paramKey = key as keyof typeof params;
      if (params[paramKey] === "" || params[paramKey] === undefined) {
        delete (params as any)[paramKey];
      }
    });

    const response = await getInvoiceAuditList(params);
    if (response.code === 200) {
      items.value = response.data.records;
      totalRecords.value = response.data.page.total;
    } else {
      toast.add({
        severity: "error",
        summary: "错误",
        detail: (response as any).msg || "获取数据失败",
        life: 3000,
      });
    }
  } catch (error) {
    console.error("获取数据失败:", error);
    toast.add({
      severity: "error",
      summary: "错误",
      detail: "获取数据失败",
      life: 3000,
    });
  } finally {
    loading.value = false;
  }
};

// 搜索
const handleSearch = () => {
  searchForm.value.page = 1;
  loadData();
};

// 重置搜索
const handleReset = () => {
  searchForm.value = {
    account_seq: "",
    created_at: "",
    customer_num: "",
    group_approved_state: "",
    page: 1,
    pageSize: 20,
  };
  createdAtDate.value = null;
  loadData();
};

// 分页变化
const onPageChange = (event: any) => {
  searchForm.value.page = event.page + 1;
  searchForm.value.pageSize = event.rows;
  loadData();
};

// 导出数据
const handleExport = async () => {
  exportLoading.value = true;
  try {
    const params = { ...searchForm.value };
    // 移除分页参数
    delete params.page;
    delete params.pageSize;

    const blob = await exportInvoiceAudit(params);

    // 创建下载链接
    const url = window.URL.createObjectURL(blob.blob);
    const link = document.createElement("a");
    link.href = url;
    link.download = blob.filename || "审核过程明细.xlsx";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);

    toast.add({
      severity: "success",
      summary: "成功",
      detail: "数据导出成功",
      life: 3000,
    });
  } catch (error) {
    console.error("导出失败:", error);
    toast.add({
      severity: "error",
      summary: "错误",
      detail: "数据导出失败",
      life: 3000,
    });
  } finally {
    exportLoading.value = false;
  }
};

// 返回预开票状态
const handleRevertToPending = async (item: InvoiceAuditItem) => {
  try {
    const response = await revertAuditToPending(item.id);
    if (response.code === 200) {
      toast.add({
        severity: "success",
        summary: "成功",
        detail: "已返回预开票状态",
        life: 3000,
      });
      loadData();
    } else {
      toast.add({
        severity: "error",
        summary: "错误",
        detail: (response as any).msg || "操作失败",
        life: 3000,
      });
    }
  } catch (error) {
    console.error("返回预开票状态失败:", error);
    toast.add({
      severity: "error",
      summary: "错误",
      detail: "操作失败",
      life: 3000,
    });
  }
};

// 加载发票详情数据
const loadInvoiceDetail = async (
  invoiceId: number,
  page = 1,
  pageSize = 10
) => {
  if (!detailSearchParams.value[invoiceId]) {
    detailSearchParams.value[invoiceId] = {
      page: 1,
      pageSize: 10,
    };
  }

  detailSearchParams.value[invoiceId].page = page;
  detailSearchParams.value[invoiceId].pageSize = pageSize;

  detailLoading.value[invoiceId] = true;
  try {
    const response = await getPreInvoicingDetail(
      invoiceId,
      detailSearchParams.value[invoiceId]
    );
    if (response.code === 200) {
      detailData.value[invoiceId] = response.data.records;
      detailTotalRecords.value[invoiceId] = response.data.page.total;
    } else {
      toast.add({
        severity: "error",
        summary: "错误",
        detail: response.message || "获取详情数据失败",
        life: 3000,
      });
    }
  } catch (error) {
    toast.add({
      severity: "error",
      summary: "错误",
      detail: "获取详情数据失败",
      life: 3000,
    });
  } finally {
    detailLoading.value[invoiceId] = false;
  }
};

// 处理行展开
const onRowExpand = (event: any) => {
  const invoiceId = event.data.id;
  loadInvoiceDetail(invoiceId);
};

// 处理行折叠
const onRowCollapse = (event: any) => {
  const invoiceId = event.data.id;
  // 清理相关数据
  delete detailData.value[invoiceId];
  delete detailLoading.value[invoiceId];
  delete detailTotalRecords.value[invoiceId];
  delete detailSearchParams.value[invoiceId];
};

// 处理详情分页变化
const onDetailPageChange = (invoiceId: number, event: any) => {
  loadInvoiceDetail(invoiceId, event.page + 1, event.rows);
};

// 格式化金额
const formatAmount = (amount: number | string) => {
  const numAmount = typeof amount === "string" ? parseFloat(amount) : amount;
  return `¥${numAmount.toLocaleString()}`;
};

// 格式化状态
const formatState = (state: string) => {
  const stateMap: Record<string, string> = {
    submitted: "审批中",
    approved: "已审批",
    rejected: "被驳回",
  };
  return stateMap[state] || state;
};

// 获取状态标签样式
const getStateSeverity = (state: string) => {
  const severityMap: Record<string, string> = {
    submitted: "info",
    approved: "success",
    rejected: "danger",
  };
  return severityMap[state] || "info";
};

onMounted(() => {
  loadCustomerOptions();
  loadData();
});
</script>

<template>
  <!-- 搜索工具栏 -->
  <Toolbar class="mb-2">
    <template #start>
      <div class="flex items-center gap-4">
        <Message severity="info">审核过程</Message>
      </div>
    </template>
    <template #end>
      <div class="flex flex-wrap gap-2 items-center">
        <Select
          v-model="searchForm.customer_num"
          :options="customerOptions"
          optionLabel="customer_name"
          optionValue="customer_num"
          placeholder="请选择客户"
          class="w-48"
          showClear
          filter
        />
        <FloatLabel>
          <label class="text-sm font-medium text-gray-700 mb-1">分账序号</label>
          <InputText v-model="searchForm.account_seq" class="w-48" />
        </FloatLabel>
        <DatePicker
          v-model="createdAtDate"
          dateFormat="yy-mm-dd"
          placeholder="请选择开票时间"
          showIcon
          class="w-60"
          @date-select="onCreatedAtChange"
        />
        <Select
          v-model="searchForm.group_approved_state"
          :options="approvalStatusOptions"
          optionLabel="label"
          optionValue="value"
          placeholder="请选择审批状态"
          class="w-48"
          showClear
        />
        <Button @click="handleSearch" icon="pi pi-search" rounded />
        <Button
          @click="handleReset"
          icon="pi pi-refresh"
          class="p-button-secondary"
          rounded
        />
      </div>
      <Divider layout="vertical" />
      <Button
        icon="pi pi-file-export"
        @click="handleExport"
        :loading="exportLoading"
        severity="help"
      />
    </template>
  </Toolbar>

  <!-- 数据表格 -->
  <DataTable
    :value="items"
    v-model:expandedRows="expandedRows"
    :loading="loading"
    :paginator="true"
    :rows="searchForm.pageSize"
    :totalRecords="totalRecords"
    :lazy="true"
    @page="onPageChange"
    @row-expand="onRowExpand"
    @row-collapse="onRowCollapse"
    paginatorTemplate="FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink CurrentPageReport RowsPerPageDropdown"
    :rowsPerPageOptions="[10, 20, 50]"
    currentPageReportTemplate="显示第 {first} 到 {last} 条记录，共 {totalRecords} 条"
    class="p-datatable-sm"
    showGridlines
    scrollable
    scrollHeight="calc(100vh - 20rem)"
    dataKey="id"
  >
    <template #empty>
      <div class="empty-message">
        <i
          class="pi pi-inbox"
          style="
            font-size: 2rem;
            color: var(--p-text-color-secondary);
            margin-bottom: 1rem;
          "
        ></i>
        <p>暂无审核数据</p>
      </div>
    </template>

    <Column :expander="true" headerStyle="width: 3rem"></Column>

    <Column field="invoice_no" header="发票编号" style="min-width: 18rem">
      <template #body="{ data }">
        <span class="font-mono text-sm">{{ data.invoice_no }}</span>
      </template>
    </Column>

    <Column field="customer_name" header="客户名称" style="min-width: 15rem">
      <template #body="{ data }">
        <span class="font-medium">{{ data.customer_name }}</span>
      </template>
    </Column>

    <Column field="account_seq" header="分账序号" style="min-width: 12rem">
      <template #body="{ data }">
        <span class="font-mono text-sm">{{ data.account_seq }}</span>
      </template>
    </Column>

    <Column field="amount" header="金额" style="min-width: 10rem">
      <template #body="{ data }">
        <span class="font-semibold text-green-600">{{
          formatAmount(data.amount)
        }}</span>
      </template>
    </Column>

    <Column field="invoice_type" header="发票类型" style="min-width: 8rem">
      <template #body="{ data }">
        <Tag :value="data.invoice_type" />
      </template>
    </Column>

    <Column field="currency_type" header="币种" style="min-width: 6rem">
      <template #body="{ data }">
        <Tag :value="data.currency_type" class="p-tag-secondary" />
      </template>
    </Column>

    <Column
      field="group_approved_state"
      header="审批状态"
      style="min-width: 8rem"
    >
      <template #body="{ data }">
        <Tag
          :value="formatState(data.group_approved_state)"
          :severity="getStateSeverity(data.group_approved_state)"
        />
      </template>
    </Column>

    <Column field="create_user" header="创建者" style="min-width: 8rem">
      <template #body="{ data }">
        <span>{{ data.create_user }}</span>
      </template>
    </Column>

    <Column field="created_at" header="开票时间" style="min-width: 12rem">
      <template #body="{ data }">
        <span>{{ formatDateTime(data.created_at) }}</span>
      </template>
    </Column>

    <Column header="操作" style="min-width: 10rem" frozen alignFrozen="right">
      <template #body="{ data }">
        <div class="flex gap-2">
          <Button
            v-if="data.group_approved_state === 'rejected'"
            icon="pi pi-undo"
            size="small"
            severity="warn"
            outlined
            @click="handleRevertToPending(data)"
            v-tooltip.top="'返回预开票状态'"
          />
        </div>
      </template>
    </Column>

    <!-- 行展开模板 -->
    <template #expansion="{ data }">
      <div class="p-4 bg-gray-50 border-l-3 border-blue-500">
        <Toolbar class="mb-2">
          <template #start>
            <Message severity="info">发票详情</Message>
          </template>
        </Toolbar>

        <DataTable
          :value="detailData[data.id] || []"
          :loading="detailLoading[data.id] || false"
          :paginator="true"
          :rows="10"
          :totalRecords="detailTotalRecords[data.id] || 0"
          :lazy="true"
          @page="(event) => onDetailPageChange(data.id, event)"
          paginatorTemplate="FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink CurrentPageReport"
          currentPageReportTemplate="显示第 {first} 到 {last} 条记录，共 {totalRecords} 条"
          class="p-datatable-sm detail-table"
          showGridlines
          stripedRows
        >
          <template #empty>
            <div class="empty-message">
              <i
                class="pi pi-inbox"
                style="
                  font-size: 2rem;
                  color: var(--p-text-color-secondary);
                  margin-bottom: 1rem;
                "
              ></i>
              <p>暂无详情数据</p>
            </div>
          </template>

          <!-- 根据invoice_pay_type显示不同的列 -->
          <Column
            v-if="data.invoice_pay_type !== 1"
            field="sub_order_no"
            header="子订单编号"
            style="min-width: 12rem"
          >
            <template #body="{ data: detailItem }">
              <span class="font-mono text-sm">{{
                detailItem.sub_order_no || "-"
              }}</span>
            </template>
          </Column>

          <Column
            v-if="data.invoice_pay_type !== 1"
            field="charge_month"
            header="计费月份"
            style="min-width: 8rem"
          >
            <template #body="{ data: detailItem }">
              <span>{{ detailItem.charge_month || "-" }}</span>
            </template>
          </Column>

          <Column
            v-if="data.invoice_pay_type !== 1"
            field="adjust_month"
            header="调账月份"
            style="min-width: 8rem"
          >
            <template #body="{ data: detailItem }">
              <span>{{ detailItem.adjust_month || "-" }}</span>
            </template>
          </Column>

          <Column
            v-if="data.invoice_pay_type === 1"
            field="total_num"
            header="订单编号"
            style="min-width: 12rem"
          >
            <template #body="{ data: detailItem }">
              <span class="font-mono text-sm">{{
                detailItem.total_num || "-"
              }}</span>
            </template>
          </Column>

          <Column field="target_type" header="数据类型" style="min-width: 8rem">
            <template #body="{ data: detailItem }">
              <Tag :value="detailItem.target_type" />
            </template>
          </Column>

          <Column field="amount" header="金额" style="min-width: 8rem">
            <template #body="{ data: detailItem }">
              <span class="font-semibold text-blue-600">{{
                parseFloat(detailItem.amount).toLocaleString()
              }}</span>
            </template>
          </Column>

          <Column
            v-if="data.invoice_pay_type === 1"
            field="invoice_month"
            header="开票月份"
            style="min-width: 8rem"
          >
            <template #body="{ data: detailItem }">
              <span>{{ detailItem.invoice_month || "-" }}</span>
            </template>
          </Column>

          <Column field="created_at" header="创建时间" style="min-width: 12rem">
            <template #body="{ data: detailItem }">
              <span>{{ formatDateTime(detailItem.created_at) }}</span>
            </template>
          </Column>
        </DataTable>
      </div>
    </template>
  </DataTable>
</template>

<style scoped>
.font-mono {
  font-family: "Monaco", "Menlo", "Ubuntu Mono", monospace;
}

.detail-table {
  margin-top: 1rem;
}
</style>
