<script setup lang="ts">
import { ref, onMounted } from "vue";
import {
  getPreSubmitList,
  exportPreSubmit,
  revertToPending,
  submitApproval,
} from "../../services/invoice";
import type {
  PreSubmitItem,
  PreSubmitSearchParams,
  SubmitApprovalRequest,
} from "../../types/invoice";
import { getCustomersSimpleList } from "../../services/customer";
import type { CustomerSimpleInfo } from "../../types/customer";
import { useToast } from "primevue/usetoast";
import { formatDateTime } from "../../utils/common";

const toast = useToast();
const loading = ref(false);
const exportLoading = ref(false);
const items = ref<PreSubmitItem[]>([]);
const totalRecords = ref(0);
const selectedItems = ref<PreSubmitItem[]>([]);

// 筛选表单
const searchForm = ref<PreSubmitSearchParams>({
  account_seq: "",
  create_user: "",
  customer_num: "",
  page: 1,
  pageSize: 20,
});

// 客户选项
const customerOptions = ref<CustomerSimpleInfo[]>([]);

// 加载客户列表
const loadCustomerOptions = async () => {
  try {
    const response = await getCustomersSimpleList();
    if (response.code === 200) {
      customerOptions.value = response.data;
    }
  } catch (error) {
    toast.add({
      severity: "error",
      summary: "错误",
      detail: "加载客户列表失败",
      life: 3000,
    });
  }
};

// 获取数据
const loadData = async () => {
  loading.value = true;
  try {
    const params = {
      ...searchForm.value,
      page: searchForm.value.page || 1,
      pageSize: searchForm.value.pageSize || 20,
    };

    // 移除空值参数
    Object.keys(params).forEach((key) => {
      const paramKey = key as keyof typeof params;
      if (params[paramKey] === "" || params[paramKey] === undefined) {
        delete (params as any)[paramKey];
      }
    });

    const response = await getPreSubmitList(params);
    if (response.code === 200) {
      items.value = response.data.records;
      totalRecords.value = response.data.page.total;
    } else {
      toast.add({
        severity: "error",
        summary: "错误",
        detail: (response as any).msg || "获取数据失败",
        life: 3000,
      });
    }
  } catch (error) {
    console.error("获取数据失败:", error);
    toast.add({
      severity: "error",
      summary: "错误",
      detail: "获取数据失败",
      life: 3000,
    });
  } finally {
    loading.value = false;
  }
};

// 搜索
const handleSearch = () => {
  searchForm.value.page = 1;
  loadData();
};

// 重置搜索
const handleReset = () => {
  searchForm.value = {
    account_seq: "",
    create_user: "",
    customer_num: "",
    page: 1,
    pageSize: 20,
  };
  loadData();
};

// 分页变化
const onPageChange = (event: any) => {
  searchForm.value.page = event.page + 1;
  searchForm.value.pageSize = event.rows;
  loadData();
};

// 导出数据
const handleExport = async () => {
  exportLoading.value = true;
  try {
    const params = { ...searchForm.value };
    // 移除分页参数
    delete params.page;
    delete params.pageSize;

    const blob = await exportPreSubmit(params);

    // 创建下载链接
    const url = window.URL.createObjectURL(blob.blob);
    const link = document.createElement("a");
    link.href = url;
    // 从响应中获取文件名，如果没有则使用默认值
    link.download = blob.filename || "待提交明细.xlsx";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);

    toast.add({
      severity: "success",
      summary: "成功",
      detail: "数据导出成功",
      life: 3000,
    });
  } catch (error) {
    console.error("导出失败:", error);
    toast.add({
      severity: "error",
      summary: "错误",
      detail: "数据导出失败",
      life: 3000,
    });
  } finally {
    exportLoading.value = false;
  }
};

// 返回预开票状态
const handleRevertToPending = async (item: PreSubmitItem) => {
  try {
    const response = await revertToPending(item.id);
    if (response.code === 200) {
      toast.add({
        severity: "success",
        summary: "成功",
        detail: "已返回预开票状态",
        life: 3000,
      });
      loadData();
    } else {
      toast.add({
        severity: "error",
        summary: "错误",
        detail: (response as any).msg || "操作失败",
        life: 3000,
      });
    }
  } catch (error) {
    console.error("返回预开票状态失败:", error);
    toast.add({
      severity: "error",
      summary: "错误",
      detail: "操作失败",
      life: 3000,
    });
  }
};

// 部分提交审批
const handlePartialSubmit = async () => {
  if (selectedItems.value.length === 0) {
    toast.add({
      severity: "warn",
      summary: "提示",
      detail: "请先选择要提交的记录",
      life: 3000,
    });
    return;
  }

  try {
    const requestData: SubmitApprovalRequest = {
      ids: selectedItems.value.map((item) => item.id),
      action: "part",
    };

    const response = await submitApproval(requestData);
    if (response.code === 200) {
      toast.add({
        severity: "success",
        summary: "成功",
        detail: "部分提交审批成功",
        life: 3000,
      });
      selectedItems.value = [];
      loadData();
    } else {
      toast.add({
        severity: "error",
        summary: "错误",
        detail: (response as any).msg || "提交失败",
        life: 3000,
      });
    }
  } catch (error) {
    console.error("部分提交审批失败:", error);
    toast.add({
      severity: "error",
      summary: "错误",
      detail: "提交失败",
      life: 3000,
    });
  }
};

// 全部提交审批
const handleFullSubmit = async () => {
  try {
    const requestData: SubmitApprovalRequest = {
      action: "all",
    };

    const response = await submitApproval(requestData);
    if (response.code === 200) {
      toast.add({
        severity: "success",
        summary: "成功",
        detail: "全部提交审批成功",
        life: 3000,
      });
      selectedItems.value = [];
      loadData();
    } else {
      toast.add({
        severity: "error",
        summary: "错误",
        detail: (response as any).msg || "提交失败",
        life: 3000,
      });
    }
  } catch (error) {
    console.error("全部提交审批失败:", error);
    toast.add({
      severity: "error",
      summary: "错误",
      detail: "提交失败",
      life: 3000,
    });
  }
};

// 格式化金额
const formatAmount = (amount: number | string) => {
  const numAmount = typeof amount === "string" ? parseFloat(amount) : amount;
  return `¥${numAmount.toLocaleString()}`;
};

// 格式化状态
const formatState = (state: string) => {
  const stateMap: Record<string, string> = {
    draft: "待审批",
    submitted: "审批中",
    approved: "已审批",
    rejected: "被驳回",
  };
  return stateMap[state] || state;
};

// 获取状态标签样式
const getStateSeverity = (state: string) => {
  const severityMap: Record<string, string> = {
    draft: "warn",
    submitted: "info",
    approved: "success",
    rejected: "danger",
  };
  return severityMap[state] || "info";
};

onMounted(() => {
  loadCustomerOptions();
  loadData();
});
</script>

<template>
  <!-- 搜索工具栏 -->
  <Toolbar class="mb-2">
    <template #start>
      <div class="flex items-center gap-4">
        <Message severity="info">待提交</Message>
      </div>
    </template>
    <template #end>
      <div class="flex flex-wrap gap-2 items-center">
        <FloatLabel>
          <label class="text-sm font-medium text-gray-700 mb-1">分账序号</label>
          <InputText v-model="searchForm.account_seq" class="w-48" />
        </FloatLabel>
        <FloatLabel>
          <label class="text-sm font-medium text-gray-700 mb-1">创建者</label>
          <InputText v-model="searchForm.create_user" class="w-48" />
        </FloatLabel>
        <FloatLabel>
          <label class="text-sm font-medium text-gray-700 mb-1">客户</label>
          <Select
            v-model="searchForm.customer_num"
            :options="customerOptions"
            optionLabel="customer_name"
            optionValue="customer_num"
            placeholder="请选择客户"
            class="w-48"
            showClear
            filter
          />
        </FloatLabel>
        <Button @click="handleSearch" icon="pi pi-search" rounded />
        <Button
          @click="handleReset"
          icon="pi pi-refresh"
          class="p-button-secondary"
          rounded
        />
      </div>
      <Divider layout="vertical" />
      <Button
        icon="pi pi-check"
        label="部分审批"
        @click="handlePartialSubmit"
        :disabled="selectedItems.length === 0"
        severity="success"
        class="mr-2"
      />
      <Button
        icon="pi pi-check-circle"
        label="全部审批"
        @click="handleFullSubmit"
        severity="warn"
        class="mr-2"
      />
      <Button
        icon="pi pi-file-export"
        @click="handleExport"
        :loading="exportLoading"
        severity="help"
      />
    </template>
  </Toolbar>

  <!-- 数据表格 -->
  <DataTable
    :value="items"
    v-model:selection="selectedItems"
    :loading="loading"
    :paginator="true"
    :rows="searchForm.pageSize"
    :totalRecords="totalRecords"
    :lazy="true"
    @page="onPageChange"
    paginatorTemplate="FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink CurrentPageReport RowsPerPageDropdown"
    :rowsPerPageOptions="[10, 20, 50]"
    currentPageReportTemplate="显示第 {first} 到 {last} 条记录，共 {totalRecords} 条"
    class="p-datatable-sm"
    showGridlines
    scrollable
    scrollHeight="calc(100vh - 20rem)"
    dataKey="id"
  >
    <template #empty>
      <div class="empty-message">
        <i
          class="pi pi-inbox"
          style="
            font-size: 2rem;
            color: var(--p-text-color-secondary);
            margin-bottom: 1rem;
          "
        ></i>
        <p>暂无待提交数据</p>
      </div>
    </template>

    <Column selectionMode="multiple" headerStyle="width: 3rem"></Column>

    <Column field="invoice_no" header="发票编号" style="min-width: 18rem">
      <template #body="{ data }">
        <span class="font-mono text-sm">{{ data.invoice_no }}</span>
      </template>
    </Column>

    <Column field="customer_name" header="客户名称" style="min-width: 15rem">
      <template #body="{ data }">
        <span class="font-medium">{{ data.customer_name }}</span>
      </template>
    </Column>

    <Column field="account_seq" header="分账序号" style="min-width: 12rem">
      <template #body="{ data }">
        <span class="font-mono text-sm">{{ data.account_seq }}</span>
      </template>
    </Column>

    <Column field="amount" header="金额" style="min-width: 10rem">
      <template #body="{ data }">
        <span class="font-semibold text-green-600">{{
          formatAmount(data.amount)
        }}</span>
      </template>
    </Column>

    <Column field="invoice_type" header="发票类型" style="min-width: 8rem">
      <template #body="{ data }">
        <Tag :value="data.invoice_type" />
      </template>
    </Column>

    <Column field="currency_type" header="币种" style="min-width: 6rem">
      <template #body="{ data }">
        <Tag :value="data.currency_type" class="p-tag-secondary" />
      </template>
    </Column>

    <Column field="group_approved_state" header="状态" style="min-width: 8rem">
      <template #body="{ data }">
        <Tag
          :value="formatState(data.group_approved_state)"
          :severity="getStateSeverity(data.group_approved_state)"
        />
      </template>
    </Column>

    <Column field="create_user" header="创建者" style="min-width: 8rem">
      <template #body="{ data }">
        <span>{{ data.create_user }}</span>
      </template>
    </Column>

    <Column field="created_at" header="创建时间" style="min-width: 12rem">
      <template #body="{ data }">
        <span>{{ formatDateTime(data.created_at) }}</span>
      </template>
    </Column>

    <Column header="操作" style="min-width: 10rem" frozen alignFrozen="right">
      <template #body="{ data }">
        <div class="flex gap-2">
          <Button
            icon="pi pi-undo"
            size="small"
            severity="warn"
            outlined
            @click="handleRevertToPending(data)"
            v-tooltip.top="'返回预开票状态'"
          />
        </div>
      </template>
    </Column>
  </DataTable>
</template>

<style scoped>
.font-mono {
  font-family: "Monaco", "Menlo", "Ubuntu Mono", monospace;
}
</style>
